group=io.github.danielliu1123
version=0.1.0-SNAPSHOT

# Spring related
# https://github.com/spring-projects/spring-boot
springBootVersion=3.5.0
# https://github.com/springdoc/springdoc-openapi
springDocsVersion=2.8.8
# https://central.sonatype.com/artifact/io.grpc/grpc-protobuf/dependencies
protobufVersion=3.25.5
# https://central.sonatype.com/artifact/io.grpc/grpc-protobuf/dependencies
protoGoogleCommonProtosVersion=2.51.0
# https://central.sonatype.com/artifact/org.projectlombok/lombok
lombokVersion=1.18.38
# https://central.sonatype.com/artifact/org.junit.platform/junit-platform-launcher
junitPlatformVersion=1.12.2
# https://github.com/google/protobuf-gradle-plugin
protobufGradlePluginVersion=0.9.4

# Code quality
# https://plugins.gradle.org/plugin/com.diffplug.gradle.spotless
spotlessVersion=7.0.3
# https://plugins.gradle.org/plugin/com.github.spotbugs
spotbugsVersion=6.1.12
# https://github.com/spotbugs/spotbugs-gradle-plugin/blob/master/build.gradle.kts
spotbugsAnnotationsVersion=4.8.6

org.gradle.jvmargs=-Xmx2g
org.gradle.parallel=true
org.gradle.caching=true
